{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start"}, "dependencies": {"@ehr/hl7-v2": "^1.0.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "1.2.1", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.1", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.2", "@radix-ui/react-tooltip": "1.1.4", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "json-2-csv": "^5.5.9", "lucide-react": "0.468.0", "mongodb": "^6.17.0", "next": "15.1.6", "next-themes": "0.4.4", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "7.54.2", "react-resizable-panels": "2.1.7", "react-syntax-highlighter": "15.6.1", "sonner": "1.7.3", "tailwind-merge": "2.5.5", "tailwindcss-animate": "1.0.7", "xml-js": "1.6.11", "zod": "3.24.1"}, "devDependencies": {"@types/node": "22.0.0", "@types/react": "18.3.0", "@types/react-dom": "18.3.0", "postcss": "8.5.0", "tailwindcss": "3.4.17", "typescript": "5.6.3"}}